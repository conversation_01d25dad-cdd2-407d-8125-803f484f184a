@extends('layouts.dashboard')

@section('content')
    <div class="p-6">
        <!-- Welcome Section -->
        <h1 class="text-2xl text-gray-900 mb-2">
            Good Morning <span class="text-primary font-semibold">{{ $dashboardData['user']['name'] }}!</span>
        </h1>

        <div class="welcome-shadow p-6 mb-6">

            <div class="mb-8 flex justify-between">
                <div>
                    <p class="text-gray-600 font-semibold">Claim Summary Overview</p>
                    <p class="text-sm text-gray-500">Last update: 07-07-2025 - 13:59 EST</p>
                </div>

                <!-- Monthly Dropdown -->
                <div class="mt-4">
                    <select
                        class="px-4 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                        style="background-color: #2B8B7F; color: white;">
                        <option>Monthly</option>
                        <option>Weekly</option>
                        <option>Daily</option>
                    </select>
                </div>
            </div>
            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-2">

                <!-- Claims Received -->
                <div class="dashboard-widget-blue rounded-xl p-4">
                    <div class="flex items-center justify-between mb-2">
                        <div class="text-[#353535]">
                            <img src="{{ asset('assets/images/claims-received.svg') }}">
                        </div>
                        <span class="text-xs text-[#353535] bg-blue-100 px-2 py-1 rounded">View Reports</span>
                    </div>
                    <div class="text-2xl font-bold text-[#353535] mb-1">
                        <div class="text-sm font-semibold text-[#353535] mb-1">Claims Received</div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="text-xl font-semibold">{{ $dashboardData['stats']['claims_received']['value'] }}</div>
                        <div class="text-sm text-[#353535]">{{ $dashboardData['stats']['claims_received']['month'] }}</div>
                    </div>
                </div>

                <!-- Processed Claims -->
                <div class="dashboard-widget-orange rounded-xl p-4">
                    <div class="flex items-center justify-between mb-2">
                        <div class="text-[#353535]">
                            <img src="{{ asset('assets/images/claims-processed.svg') }}">
                        </div>
                        <span class="text-xs text-[#353535] bg-orange-100 px-2 py-1 rounded">View Reports</span>
                    </div>
                    <div class="text-2xl font-bold text-[#353535] mb-1">
                        <div class="text-sm font-semibold text-[#353535] mb-1">Processed Claims</div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="text-xl font-semibold">{{ $dashboardData['stats']['processed_claims']['value'] }}</div>
                        <div class="text-sm text-[#353535]">{{ $dashboardData['stats']['processed_claims']['month'] }}</div>
                    </div>
                </div>

                <!-- Outstanding Claims -->
                <div class="dashboard-widget-purple rounded-xl p-4">
                    <div class="flex items-center justify-between mb-2">
                        <div class="text-[#353535]">
                            <img src="{{ asset('assets/images/claims-outstanding.svg') }}">
                        </div>
                        <span class="text-xs text-[#353535] bg-purple-100 px-2 py-1 rounded">View Reports</span>
                    </div>
                    <div class="text-2xl font-bold text-[#353535] mb-1">
                        <div class="text-sm font-semibold text-[#353535] mb-1">Outstanding Claims</div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="text-xl font-semibold">{{ $dashboardData['stats']['outstanding_claims']['value'] }}
                        </div>
                        <div class="text-sm text-[#353535]">{{ $dashboardData['stats']['outstanding_claims']['month'] }}
                        </div>
                    </div>
                </div>

                <!-- Benefit Verification -->
                <div class="dashboard-widget-margenta rounded-xl p-4">
                    <div class="flex items-center justify-between mb-2">
                        <div class="text-[#353535]">
                            <img src="{{ asset('assets/images/claims-outstanding.svg') }}">
                        </div>
                        <span class="text-xs text-[#353535] bg-blue-100 px-2 py-1 rounded">View Reports</span>
                    </div>
                    <div class="text-2xl font-bold text-[#353535] mb-1">
                        <div class="text-sm font-semibold text-[#353535] mb-1">Benefit Verification</div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="text-xl font-semibold">{{ $dashboardData['stats']['benefit_verification']['value'] }}
                        </div>
                        <div class="text-sm text-[#353535]">{{ $dashboardData['stats']['benefit_verification']['month'] }}
                        </div>
                    </div>
                </div>

                <!-- Pre Authorization -->
                <div class="dashboard-widget-pink rounded-xl p-4">
                    <div class="flex items-center justify-between mb-2">
                        <div class="text-[#353535]">
                            <img src="{{ asset('assets/images/claims-outstanding.svg') }}">
                        </div>
                        <span class="text-xs text-[#353535] bg-pink-100 px-2 py-1 rounded">View Reports</span>
                    </div>
                    <div class="text-2xl font-bold text-[#353535] mb-1">
                        <div class="text-sm font-semibold text-[#353535] mb-1">Pre Authorization</div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="text-xl font-semibold">{{ $dashboardData['stats']['pre_authorization']['value'] }}
                        </div>
                        <div class="text-sm text-[#353535]">{{ $dashboardData['stats']['pre_authorization']['month'] }}
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Insurance Collection Chart -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Insurance Collection</h3>

                <!-- AmCharts Insurance Collection Chart -->
                <div id="insuranceCollectionChart" style="width: 100%; height: 400px;"></div>
            </div>

            <!-- Claims Status Chart -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Claims Status</h3>
                    <select class="text-sm border border-gray-300 rounded px-2 py-1">
                        <option>Monthly</option>
                        <option>Weekly</option>
                        <option>Daily</option>
                    </select>
                </div>

                <!-- AmCharts Bar Chart -->
                <div id="claimsStatusChart" style="width: 100%; height: 400px;"></div>
            </div>
        </div>

        <div class="mt-4">
            <h3 class="text-lg font-semibold text-[#595959] mb-6">Monthly Collection Summary</h3>
        </div>

        <div class="p-6 bg-white rounded-xl shadow-sm mt-6">
            <div class="grid grid-cols-1 lg:grid-cols-5 gap-6"> <!-- Changed to 5-column grid -->
                <!-- Monthly Collection Summary (now spans 3 of 5 columns) -->
                <div class="lg:col-span-4 p-6"> <!-- Changed from 2 to 3 -->
                    
                    <!-- March -->
                    <div class="flex items-center justify-between py-4 border border-[#E6E6E6] mb-3 rounded-lg px-5">
                        <div class="text-[#595959] font-medium">March</div>
                        <div class="text-[#595959] font-semibold">$500,000</div>
                    </div>

                    <!-- April -->
                    <div class="flex items-center justify-between py-4 border border-[#E6E6E6] mb-3 rounded-lg px-5">
                        <div class="text-[#595959] font-medium">April</div>
                        <div class="text-[#595959] font-semibold">$200,000</div>
                    </div>

                    <!-- May -->
                    <div class="flex items-center justify-between py-4 border border-[#E6E6E6] mb-3 rounded-lg px-5">
                        <div class="text-[#595959] font-medium">May</div>
                        <div class="text-[#595959] font-semibold">$500,000</div>
                    </div>
                </div>

                <!-- Performance Summary (now spans 2 of 5 columns) -->
                <div class="lg:col-span-1 flex items-center justify-center"> <!-- Changed from 1 to 2 -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-5">
                        <div class="text-lg text-gray-500 mb-2">Based on the previous month of collection you have </div>
                        <div class="flex items-baseline mb-2">
                            <span class="text-5xl font-bold text-primary">8%</span>
                            <span class="text-sm text-gray-600 ml-2">Increment</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-2 h-2 bg-teal-500 rounded-full mr-2"></div>
                            <span class="text-teal-600 font-semibold">$500,000</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- AmCharts Resources -->
    <script src="https://cdn.amcharts.com/lib/5/index.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/xy.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>

    <!-- Claims Status Chart Script -->
    <script>
    am5.ready(function() {
        // Create root element
        var root = am5.Root.new("claimsStatusChart");

        // Set themes
        root.setThemes([
            am5themes_Animated.new(root)
        ]);

        // Create chart
        var chart = root.container.children.push(am5xy.XYChart.new(root, {
            panX: false,
            panY: false,
            paddingLeft: 0,
            wheelX: "panX",
            wheelY: "zoomX",
            layout: root.verticalLayout
        }));

        // Add legend
        var legend = chart.children.push(
            am5.Legend.new(root, {
                centerX: am5.p50,
                x: am5.p50
            })
        );

        // Claims Status Data from Laravel
        var data = [
            @foreach($dashboardData['charts']['claims_status']['months'] as $index => $month)
            {
                "month": "{{ $month }}",
                "pending": {{ $dashboardData['charts']['claims_status']['data']['pending'][$index] }},
                "approved": {{ $dashboardData['charts']['claims_status']['data']['approved'][$index] }},
                "rejected": {{ $dashboardData['charts']['claims_status']['data']['rejected'][$index] }},
                "in_review": {{ $dashboardData['charts']['claims_status']['data']['in_review'][$index] }},
                "processed": {{ $dashboardData['charts']['claims_status']['data']['processed'][$index] }}
            }@if(!$loop->last),@endif
            @endforeach
        ];

        // Create axes
        var xRenderer = am5xy.AxisRendererX.new(root, {
            cellStartLocation: 0.1,
            cellEndLocation: 0.9,
            minorGridEnabled: true
        });

        var xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
            categoryField: "month",
            renderer: xRenderer,
            tooltip: am5.Tooltip.new(root, {})
        }));

        xRenderer.grid.template.setAll({
            location: 1
        });

        xAxis.data.setAll(data);

        var yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
            renderer: am5xy.AxisRendererY.new(root, {
                strokeOpacity: 0.1
            })
        }));

        // Add series function
        function makeSeries(name, fieldName, color) {
            var series = chart.series.push(am5xy.ColumnSeries.new(root, {
                name: name,
                xAxis: xAxis,
                yAxis: yAxis,
                valueYField: fieldName,
                categoryXField: "month"
            }));

            series.columns.template.setAll({
                tooltipText: "{name}, {categoryX}: {valueY}",
                width: am5.percent(90),
                tooltipY: 0,
                strokeOpacity: 0,
                fill: am5.color(color)
            });

            series.data.setAll(data);

            // Make stuff animate on load
            series.appear();

            series.bullets.push(function () {
                return am5.Bullet.new(root, {
                    locationY: 0,
                    sprite: am5.Label.new(root, {
                        text: "{valueY}",
                        fill: root.interfaceColors.get("alternativeText"),
                        centerY: 0,
                        centerX: am5.p50,
                        populateText: true
                    })
                });
            });

            legend.data.push(series);
        }

        // Create series with MedsDental brand colors
        makeSeries("Pending Claims", "pending", "{{ $dashboardData['charts']['claims_status']['colors']['pending'] }}");
        makeSeries("Approved Claims", "approved", "{{ $dashboardData['charts']['claims_status']['colors']['approved'] }}");
        makeSeries("Rejected Claims", "rejected", "{{ $dashboardData['charts']['claims_status']['colors']['rejected'] }}");
        makeSeries("In Review Claims", "in_review", "{{ $dashboardData['charts']['claims_status']['colors']['in_review'] }}");
        makeSeries("Processed Claims", "processed", "{{ $dashboardData['charts']['claims_status']['colors']['processed'] }}");

        // Make stuff animate on load
        chart.appear(1000, 100);

    }); // end am5.ready()

    // Insurance Collection Chart
    am5.ready(function() {
        // Create root element for Insurance Collection
        var insuranceRoot = am5.Root.new("insuranceCollectionChart");

        // Set themes
        insuranceRoot.setThemes([
            am5themes_Animated.new(insuranceRoot)
        ]);

        // Create chart
        var insuranceChart = insuranceRoot.container.children.push(am5xy.XYChart.new(insuranceRoot, {
            panX: false,
            panY: false,
            paddingLeft: 0,
            wheelX: "panX",
            wheelY: "zoomX",
            layout: insuranceRoot.verticalLayout
        }));

        // Add legend
        var insuranceLegend = insuranceChart.children.push(
            am5.Legend.new(insuranceRoot, {
                centerX: am5.p50,
                x: am5.p50
            })
        );

        // Insurance Collection Data from Laravel
        var insuranceData = [
            @foreach($dashboardData['charts']['insurance_collection']['months'] as $index => $month)
            {
                "month": "{{ $month }}",
                "primary_insurance": {{ $dashboardData['charts']['insurance_collection']['data']['primary_insurance'][$index] }},
                "secondary_insurance": {{ $dashboardData['charts']['insurance_collection']['data']['secondary_insurance'][$index] }},
                "patient_payment": {{ $dashboardData['charts']['insurance_collection']['data']['patient_payment'][$index] }},
                "adjustments": {{ $dashboardData['charts']['insurance_collection']['data']['adjustments'][$index] }}
            }@if(!$loop->last),@endif
            @endforeach
        ];

        // Create axes
        var insuranceXRenderer = am5xy.AxisRendererX.new(insuranceRoot, {
            cellStartLocation: 0.1,
            cellEndLocation: 0.9,
            minorGridEnabled: true
        });

        var insuranceXAxis = insuranceChart.xAxes.push(am5xy.CategoryAxis.new(insuranceRoot, {
            categoryField: "month",
            renderer: insuranceXRenderer,
            tooltip: am5.Tooltip.new(insuranceRoot, {})
        }));

        insuranceXRenderer.grid.template.setAll({
            location: 1
        });

        insuranceXAxis.data.setAll(insuranceData);

        var insuranceYAxis = insuranceChart.yAxes.push(am5xy.ValueAxis.new(insuranceRoot, {
            renderer: am5xy.AxisRendererY.new(insuranceRoot, {
                strokeOpacity: 0.1
            })
        }));

        // Add series function for Insurance Collection
        function makeInsuranceSeries(name, fieldName, color) {
            var series = insuranceChart.series.push(am5xy.ColumnSeries.new(insuranceRoot, {
                name: name,
                xAxis: insuranceXAxis,
                yAxis: insuranceYAxis,
                valueYField: fieldName,
                categoryXField: "month"
            }));

            series.columns.template.setAll({
                tooltipText: "{name}, {categoryX}: ${valueY}",
                width: am5.percent(90),
                tooltipY: 0,
                strokeOpacity: 0,
                fill: am5.color(color)
            });

            series.data.setAll(insuranceData);

            // Make stuff animate on load
            series.appear();

            series.bullets.push(function () {
                return am5.Bullet.new(insuranceRoot, {
                    locationY: 0,
                    sprite: am5.Label.new(insuranceRoot, {
                        text: "${valueY}",
                        fill: insuranceRoot.interfaceColors.get("alternativeText"),
                        centerY: 0,
                        centerX: am5.p50,
                        populateText: true
                    })
                });
            });

            insuranceLegend.data.push(series);
        }

        // Create series with Insurance Collection categories
        makeInsuranceSeries("Primary Insurance", "primary_insurance", "{{ $dashboardData['charts']['insurance_collection']['colors']['primary_insurance'] }}");
        makeInsuranceSeries("Secondary Insurance", "secondary_insurance", "{{ $dashboardData['charts']['insurance_collection']['colors']['secondary_insurance'] }}");
        makeInsuranceSeries("Patient Payment", "patient_payment", "{{ $dashboardData['charts']['insurance_collection']['colors']['patient_payment'] }}");
        makeInsuranceSeries("Adjustments", "adjustments", "{{ $dashboardData['charts']['insurance_collection']['colors']['adjustments'] }}");

        // Make stuff animate on load
        insuranceChart.appear(1000, 100);

    }); // end am5.ready() for Insurance Collection
    </script>
@endsection

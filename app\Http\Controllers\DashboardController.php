<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * Display the dashboard.
     */
    public function index()
    {
        $user = auth()->user();

        // Sample data for the dashboard
        $dashboardData = [
            'user' => [
                'name' => $user->name,
                'email' => $user->email,
                'role' => 'Manager' // You can add a role field to users table later
            ],
            'stats' => [
                'claims_received' => [
                    'value' => 127,
                    'month' => 'June',
                    'color' => 'bg-blue-100 text-blue-800'
                ],
                'processed_claims' => [
                    'value' => 95,
                    'month' => 'June',
                    'color' => 'bg-orange-100 text-orange-800'
                ],
                'outstanding_claims' => [
                    'value' => 10,
                    'month' => 'April',
                    'color' => 'bg-purple-100 text-purple-800'
                ],
                'benefit_verification' => [
                    'value' => 10,
                    'month' => 'April',
                    'color' => 'bg-blue-100 text-blue-800'
                ],
                'pre_authorization' => [
                    'value' => 10,
                    'month' => 'April',
                    'color' => 'bg-pink-100 text-pink-800'
                ]
            ],
            'charts' => [
                'insurance_collection' => [
                    'months' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    'data' => [
                        'primary_insurance' => [12000, 15000, 18000, 14000, 16000, 20000],
                        'secondary_insurance' => [3000, 4000, 5000, 3500, 4500, 6000],
                        'patient_payment' => [2000, 2500, 3000, 2200, 2800, 3500],
                        'adjustments' => [500, 800, 1200, 600, 900, 1100]
                    ],
                    'colors' => [
                        'primary_insurance' => '#2B8B7F',    // Brand primary color
                        'secondary_insurance' => '#42A5F5',  // Blue
                        'patient_payment' => '#66BB6A',      // Green
                        'adjustments' => '#FFA726'           // Orange
                    ],
                    'max_value' => 25000 // For scaling
                ],
                'claims_status' => [
                    'months' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    'data' => [
                        'pending' => [45, 52, 38, 65, 48, 55],
                        'approved' => [78, 85, 92, 88, 95, 102],
                        'rejected' => [12, 8, 15, 10, 7, 9],
                        'in_review' => [25, 30, 22, 35, 28, 32],
                        'processed' => [68, 75, 82, 78, 85, 90]
                    ],
                    'colors' => [
                        'pending' => '#FFA726',      // Orange - complementary to brand
                        'approved' => '#2B8B7F',     // Brand primary color
                        'rejected' => '#EF5350',     // Red
                        'in_review' => '#42A5F5',    // Blue
                        'processed' => '#66BB6A'     // Green
                    ],
                    'max_value' => 120 // For scaling the bars
                ]
            ]
        ];

        return view('dashboard.index', compact('dashboardData'));
    }
}

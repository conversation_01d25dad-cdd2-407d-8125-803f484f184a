<x-guest-layout>
    <div class="space-y-6">
        <!-- Step Indicator -->
        <div class="step-indicator relative mb-12 px-4">
            <!-- Background Line -->
            <div class="absolute top-6 left-0 right-0 h-0.5 bg-gray-300" id="background-line"></div>

            <!-- Progress Line -->
            <div class="absolute top-6 left-0 h-0.5 bg-brand transition-all duration-500" id="progress-line"
                style="width: 0%"></div>

            <!-- Steps Container -->
            <div class="relative flex justify-between items-start">
                <!-- Step 1 (Active) -->
                <div class="flex flex-col items-center text-center" style="width: 20%;">
                    <div id="step1-indicator"
                        class="w-12 h-12 rounded-full border-2 text-white flex items-center justify-center text-lg font-medium mb-3 transition-all duration-300 step-active">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="text-center">
                        <div id="step1-title" class="text-sm font-semibold mb-1 step-title">Sign Up</div>
                        <div id="step1-subtitle" class="text-xs text-gray-500 step-subtitle">Create your account</div>
                    </div>
                </div>

                <!-- Step 2 (Inactive) -->
                <div class="flex flex-col items-center text-center" style="width: 20%;">
                    <div id="step2-indicator"
                        class="w-12 h-12 rounded-full border-2 border-gray-300 bg-gray-100 text-gray-400 flex items-center justify-center text-lg font-medium mb-3 transition-all duration-300">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z">
                            </path>
                            <path fill-rule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z"
                                clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="text-center">
                        <div id="step2-title" class="text-sm font-semibold text-gray-500 mb-1">Pricing Plan & Add.
                            Services</div>
                        <div id="step2-subtitle" class="text-xs text-gray-400">Let's finalize your plan</div>
                    </div>
                </div>

                <!-- Step 3 -->
                <div class="flex flex-col items-center text-center" style="width: 20%;">
                    <div id="step3-indicator"
                        class="w-12 h-12 rounded-full border-2 border-gray-300 bg-gray-100 text-gray-400 flex items-center justify-center text-lg font-medium mb-3 transition-all duration-300">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-6a1 1 0 00-1-1H9a1 1 0 00-1 1v6a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z"
                                clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="text-center">
                        <div id="step3-title" class="text-sm font-semibold text-gray-500 mb-1">Practice Information
                        </div>
                        <div id="step3-subtitle" class="text-xs text-gray-400">Let's get your Practice Information</div>
                    </div>
                </div>

                <!-- Step 4 -->
                <div class="flex flex-col items-center text-center" style="width: 20%;">
                    <div id="step4-indicator"
                        class="w-12 h-12 rounded-full border-2 border-gray-300 bg-gray-100 text-gray-400 flex items-center justify-center text-lg font-medium mb-3 transition-all duration-300">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z"
                                clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="text-center">
                        <div id="step4-title" class="text-sm font-semibold text-gray-500 mb-1">Terms & Agreement</div>
                        <div id="step4-subtitle" class="text-xs text-gray-400">Accept terms & conditions</div>
                    </div>
                </div>

                <!-- Step 5 -->
                <div class="flex flex-col items-center text-center" style="width: 20%;">
                    <div id="step5-indicator"
                        class="w-12 h-12 rounded-full border-2 border-gray-300 bg-gray-100 text-gray-400 flex items-center justify-center text-lg font-medium mb-3 transition-all duration-300">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                                clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="text-center">
                        <div id="step5-title" class="text-sm font-semibold text-gray-500 mb-1">Schedule A Call</div>
                        <div id="step5-subtitle" class="text-xs text-gray-400">Let's schedule a call</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Header -->
        <div class="text-center">
            <h2 id="form-title" class="text-2xl font-semibold text-gray-900 mb-2">Create your account</h2>
            <p id="form-subtitle" class="text-gray-600 text-sm">Welcome to Meds Dental! Your gateway to streamlined
                dental career growth begins here</p>
        </div>

        <!-- Google Sign Up Button (Only on Step 1) -->
        <div id="google-signup-section">
            <button type="button"
                class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                <svg class="w-5 h-5 mr-3" viewBox="0 0 24 24">
                    <path fill="#4285F4"
                        d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
                    <path fill="#34A853"
                        d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
                    <path fill="#FBBC05"
                        d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />
                    <path fill="#EA4335"
                        d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
                </svg>
                Google
            </button>

            <!-- Divider -->
            <div class="relative mt-6">
                <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-300"></div>
                </div>
                <div class="relative flex justify-center text-sm">
                    <span class="px-2 bg-gray-50 text-gray-500">Or</span>
                </div>
            </div>
        </div>

        <!-- Multi-Step Registration Form -->
        <form id="registration-form" method="POST" action="{{ route('register') }}" class="space-y-4">
            @csrf

            <!-- Step 1: Basic Information -->
            <div id="step-1" class="step-content">
                <!-- Name Fields Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- First Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                        <input id="name" name="name" type="text" value="{{ old('name') }}" required
                            autofocus autocomplete="given-name"
                            class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand focus:border-transparent placeholder-gray-400"
                            placeholder="Enter your first name">
                        <div class="error-message mt-1 text-sm text-red-600 hidden"></div>
                    </div>

                    <!-- Last Name -->
                    <div>
                        <label for="last_name" class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                        <input id="last_name" name="last_name" type="text" value="{{ old('last_name') }}"
                            autocomplete="family-name"
                            class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand focus:border-transparent placeholder-gray-400"
                            placeholder="Enter your last name">
                        <div class="error-message mt-1 text-sm text-red-600 hidden"></div>
                    </div>
                </div>

                <!-- Email and Phone Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Email Address -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email
                            Address</label>
                        <input id="email" name="email" type="email" value="{{ old('email') }}" required
                            autocomplete="email"
                            class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand focus:border-transparent placeholder-gray-400"
                            placeholder="Enter your email address">
                        <div class="error-message mt-1 text-sm text-red-600 hidden"></div>
                    </div>

                    <!-- Phone Number -->
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone
                            Number</label>
                        <input id="phone" name="phone" type="tel" value="{{ old('phone') }}"
                            autocomplete="tel"
                            class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand focus:border-transparent placeholder-gray-400"
                            placeholder="******-456-7890">
                        <div class="error-message mt-1 text-sm text-red-600 hidden"></div>
                    </div>
                </div>

                <!-- Password Fields Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Password -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                        <input id="password" name="password" type="password" required autocomplete="new-password"
                            class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand focus:border-transparent placeholder-gray-400"
                            placeholder="••••••••">
                        <div class="error-message mt-1 text-sm text-red-600 hidden"></div>
                    </div>

                    <!-- Confirm Password -->
                    <div>
                        <label for="password_confirmation"
                            class="block text-sm font-medium text-gray-700 mb-1">Confirm Password</label>
                        <input id="password_confirmation" name="password_confirmation" type="password" required
                            autocomplete="new-password"
                            class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand focus:border-transparent placeholder-gray-400"
                            placeholder="••••••••">
                        <div class="error-message mt-1 text-sm text-red-600 hidden"></div>
                    </div>
                </div>
            </div>

            <!-- Step 2: Practice & Services -->
            <div id="step-2" class="step-content hidden">
                <!-- Practice Type -->
                {{-- <div class="mb-8">
                    <div class="max-w-md mx-auto">
                        <label for="practice_type" class="block text-sm font-medium text-gray-700 mb-1">Practice Type</label>
                        <select id="practice_type" name="practice_type" required
                                class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand focus:border-transparent">
                            <option value="">Select Practice Type</option>
                            <option value="general_dentistry">General Dentistry</option>
                            <option value="orthodontics">Orthodontics</option>
                            <option value="oral_surgery">Oral Surgery</option>
                            <option value="pediatric_dentistry">Pediatric Dentistry</option>
                            <option value="periodontics">Periodontics</option>
                            <option value="endodontics">Endodontics</option>
                        </select>
                        <div class="error-message mt-1 text-sm text-red-600 hidden"></div>
                    </div>
                </div> --}}

                <!-- Pricing Plans -->
                <div class="mb-8">
                    <div class="text-center mb-6">
                        {{-- <h3 class="text-2xl font-semibold text-gray-900 mb-2">Choose Your Plan</h3>
                        <p class="text-gray-600 mb-6">Your package is included with 10 benefit verifications & 5 Pre Authorization</p> --}}

                        <!-- Billing Toggle -->
                        <div class="flex justify-center">
                            <div class="bg-gray-100 p-1 rounded-lg flex">
                                <button type="button" id="monthly-btn"
                                    class="px-6 py-3 rounded-md text-sm font-medium brand-bg text-white">Monthly</button>
                                <button type="button" id="annually-btn"
                                    class="px-6 py-3 rounded-md text-sm font-medium text-gray-600">Annually</button>
                            </div>
                        </div>
                    </div>

                    <!-- Pricing Cards -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 max-w-5xl mx-auto mb-8">
                        <!-- Small Plan -->
                        <div class="pricing-card border border-gray-200 rounded-xl p-6 cursor-pointer hover:border-brand transition-all"
                            data-plan="small">
                            <div class="text-center">
                                <div
                                    class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <span class="text-blue-600 text-lg">?</span>
                                </div>
                                <h4 class="text-lg font-semibold text-gray-900 mb-3">Small</h4>
                                <div class="text-3xl font-bold text-gray-900 mb-2">
                                    <span class="monthly-price">$1,250</span>
                                    <span class="annually-price hidden">$12,500</span>
                                </div>
                                <p class="text-sm text-gray-500 mb-6">
                                    <span class="monthly-desc">10 or collection of less than $50,000</span>
                                    <span class="annually-desc hidden">Annual billing - 10% discount</span>
                                </p>
                                <ul class="text-sm text-left space-y-3">
                                    <li class="flex items-center"><span
                                            class="text-green-500 mr-3 text-lg">✓</span>Daily electronic and paper
                                        claim submission</li>
                                    <li class="flex items-center"><span
                                            class="text-green-500 mr-3 text-lg">✓</span>Claim correction and/or
                                        re-submission</li>
                                    <li class="flex items-center"><span
                                            class="text-green-500 mr-3 text-lg">✓</span>Charge entry</li>
                                    <li class="flex items-center"><span
                                            class="text-green-500 mr-3 text-lg">✓</span>Government, commercial and
                                        private billing</li>
                                    <li class="flex items-center"><span
                                            class="text-green-500 mr-3 text-lg">✓</span>Dental management</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Medium Plan -->
                        <div class="pricing-card border border-gray-200 rounded-xl p-6 cursor-pointer hover:border-brand transition-all"
                            data-plan="medium">
                            <div class="text-center">
                                <div
                                    class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <span class="text-blue-600 text-lg">?</span>
                                </div>
                                <h4 class="text-lg font-semibold text-gray-900 mb-3">Medium</h4>
                                <div class="text-3xl font-bold text-gray-900 mb-2">
                                    <span class="monthly-price">4.99%</span>
                                    <span class="annually-price hidden">4.49%</span>
                                </div>
                                <p class="text-sm text-gray-500 mb-6">
                                    <span class="monthly-desc">@ collection of $50,000.00 or above.</span>
                                    <span class="annually-desc hidden">Annual billing - 0.5% discount</span>
                                </p>
                                <ul class="text-sm text-left space-y-3">
                                    <li class="flex items-center"><span
                                            class="text-green-500 mr-3 text-lg">✓</span>Daily electronic and paper
                                        claim submission</li>
                                    <li class="flex items-center"><span
                                            class="text-green-500 mr-3 text-lg">✓</span>Claim correction and/or
                                        re-submission</li>
                                    <li class="flex items-center"><span
                                            class="text-green-500 mr-3 text-lg">✓</span>Charge entry</li>
                                    <li class="flex items-center"><span
                                            class="text-green-500 mr-3 text-lg">✓</span>Government, commercial and
                                        private billing</li>
                                    <li class="flex items-center"><span
                                            class="text-green-500 mr-3 text-lg">✓</span>Dental management</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Large Plan (Recommended) -->
                        <div class="pricing-card border-2 border-brand rounded-xl p-6 cursor-pointer relative transition-all"
                            data-plan="large">
                            <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                                <span
                                    class="bg-brand text-white px-4 py-2 rounded-full text-sm font-medium">Recommended</span>
                            </div>
                            <div class="text-center">
                                <div
                                    class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <span class="text-blue-600 text-lg">?</span>
                                </div>
                                <h4 class="text-lg font-semibold text-gray-900 mb-3">Large</h4>
                                <div class="text-3xl font-bold text-gray-900 mb-2">
                                    <span class="monthly-price">3.49%</span>
                                    <span class="annually-price hidden">3.19%</span>
                                </div>
                                <p class="text-sm text-gray-500 mb-6">
                                    <span class="monthly-desc">@ collection of $100,000.00 or above.</span>
                                    <span class="annually-desc hidden">Annual billing - 0.3% discount</span>
                                </p>
                                <ul class="text-sm text-left space-y-3">
                                    <li class="flex items-center"><span
                                            class="text-green-500 mr-3 text-lg">✓</span>Daily electronic and paper
                                        claim submission</li>
                                    <li class="flex items-center"><span
                                            class="text-green-500 mr-3 text-lg">✓</span>Claim correction and/or
                                        re-submission</li>
                                    <li class="flex items-center"><span
                                            class="text-green-500 mr-3 text-lg">✓</span>Charge entry</li>
                                    <li class="flex items-center"><span
                                            class="text-green-500 mr-3 text-lg">✓</span>Government, commercial and
                                        private billing</li>
                                    <li class="flex items-center"><span
                                            class="text-green-500 mr-3 text-lg">✓</span>Dental management</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden input for selected plan -->
                    <input type="hidden" id="selected_plan" name="selected_plan" value="">
                    <input type="hidden" id="billing_cycle" name="billing_cycle" value="monthly">
                </div>

                <!-- Additional Services -->
                <div class="mb-8">
                    <div class="text-center mb-6">
                        <h3 class="text-2xl font-semibold text-gray-900 mb-2">Additional Services</h3>
                        <p class="text-gray-600">Enhance your plan with these optional services</p>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
                        <!-- Benefit Verification -->
                        <div
                            class="border border-gray-200 rounded-xl p-6 text-center hover:border-brand transition-all cursor-pointer">
                            <div
                                class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span class="text-green-600 text-xl">⚡</span>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-3">Benefit Verification</h4>
                            <div class="text-2xl font-bold text-gray-900 mb-2">$5</div>
                            <p class="text-sm text-gray-500 mb-4">Per Request</p>
                            <label class="flex items-center justify-center cursor-pointer">
                                <input type="checkbox" name="additional_services[]" value="benefit_verification"
                                    class="mr-3 w-4 h-4 text-brand border-gray-300 rounded focus:ring-brand">
                                <span class="text-sm font-medium">Add to plan</span>
                            </label>
                        </div>

                        <!-- Pre Authorization -->
                        <div
                            class="border border-gray-200 rounded-xl p-6 text-center hover:border-brand transition-all cursor-pointer">
                            <div
                                class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span class="text-blue-600 text-xl">⚡</span>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-3">Pre Authorization</h4>
                            <div class="text-2xl font-bold text-gray-900 mb-2">$10</div>
                            <p class="text-sm text-gray-500 mb-4">Per Request</p>
                            <label class="flex items-center justify-center cursor-pointer">
                                <input type="checkbox" name="additional_services[]" value="pre_authorization"
                                    class="mr-3 w-4 h-4 text-brand border-gray-300 rounded focus:ring-brand">
                                <span class="text-sm font-medium">Add to plan</span>
                            </label>
                        </div>

                        <!-- Dental Auditing -->
                        <div
                            class="border border-gray-200 rounded-xl p-6 text-center hover:border-brand transition-all cursor-pointer">
                            <div
                                class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span class="text-purple-600 text-xl">⚡</span>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-900 mb-3">Dental Auditing</h4>
                            <div class="text-2xl font-bold text-gray-900 mb-2">$350</div>
                            <p class="text-sm text-gray-500 mb-4">Per Month</p>
                            <label class="flex items-center justify-center cursor-pointer">
                                <input type="checkbox" name="additional_services[]" value="dental_auditing"
                                    class="mr-3 w-4 h-4 text-brand border-gray-300 rounded focus:ring-brand">
                                <span class="text-sm font-medium">Add to plan</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 3: Practice Information -->
            <div id="step-3" class="step-content hidden">
                <div class="text-center mb-8">
                    <h3 class="text-2xl font-semibold text-gray-900 mb-2">Practice Information</h3>
                    <p class="text-gray-600">Let's get your Practice Information</p>
                </div>

                <div class="max-w-4xl mx-auto">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Practice Name -->
                        <div class="md:col-span-2">
                            <label for="practice_name" class="block text-sm font-medium text-gray-700 mb-1">
                                Practice Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="practice_name" name="practice_name" required
                                placeholder="Select Practice"
                                class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand focus:border-transparent">
                            <div class="error-message mt-1 text-sm text-red-600 hidden"></div>
                        </div>

                        <!-- Practice Address -->
                        <div class="md:col-span-2">
                            <label for="practice_address" class="block text-sm font-medium text-gray-700 mb-1">
                                Practice Address <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="practice_address" name="practice_address" required
                                placeholder="Enter Full Address"
                                class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand focus:border-transparent">
                            <div class="error-message mt-1 text-sm text-red-600 hidden"></div>
                        </div>

                        <!-- Tax ID -->
                        <div class="md:col-span-2">
                            <label for="tax_id" class="block text-sm font-medium text-gray-700 mb-1">
                                Tax ID <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="tax_id" name="tax_id" required
                                placeholder="Enter Your Tax ID"
                                class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand focus:border-transparent">
                            <div class="error-message mt-1 text-sm text-red-600 hidden"></div>
                        </div>

                        <!-- Major Services -->
                        <div>
                            <label for="major_services" class="block text-sm font-medium text-gray-700 mb-1">Major
                                Services</label>
                            <select id="major_services" name="major_services"
                                class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand focus:border-transparent">
                                <option value="">Select Services</option>
                                <option value="general_dentistry">General Dentistry</option>
                                <option value="orthodontics">Orthodontics</option>
                                <option value="oral_surgery">Oral Surgery</option>
                                <option value="pediatric_dentistry">Pediatric Dentistry</option>
                                <option value="periodontics">Periodontics</option>
                                <option value="endodontics">Endodontics</option>
                                <option value="cosmetic_dentistry">Cosmetic Dentistry</option>
                            </select>
                            <div class="error-message mt-1 text-sm text-red-600 hidden"></div>
                        </div>

                        <!-- Number of Providers -->
                        <div>
                            <label for="num_providers" class="block text-sm font-medium text-gray-700 mb-1">No. Of
                                Providers</label>
                            <input type="number" id="num_providers" name="num_providers" min="1"
                                placeholder="0"
                                class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand focus:border-transparent">
                            <div class="error-message mt-1 text-sm text-red-600 hidden"></div>
                        </div>

                        <!-- Individual NPI -->
                        <div>
                            <label for="individual_npi" class="block text-sm font-medium text-gray-700 mb-1">
                                Individual NPI <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="individual_npi" name="individual_npi" required
                                placeholder="NPI"
                                class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand focus:border-transparent">
                            <div class="error-message mt-1 text-sm text-red-600 hidden"></div>
                        </div>

                        <!-- Individual PTAN -->
                        <div>
                            <label for="individual_ptan" class="block text-sm font-medium text-gray-700 mb-1">
                                Individual PTAN <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="individual_ptan" name="individual_ptan" required
                                placeholder="PTAN"
                                class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand focus:border-transparent">
                            <div class="error-message mt-1 text-sm text-red-600 hidden"></div>
                        </div>

                        <!-- Group NPI -->
                        <div>
                            <label for="group_npi" class="block text-sm font-medium text-gray-700 mb-1">Group
                                NPI</label>
                            <input type="text" id="group_npi" name="group_npi" placeholder="NPI"
                                class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand focus:border-transparent">
                            <div class="error-message mt-1 text-sm text-red-600 hidden"></div>
                        </div>

                        <!-- Group PTAN -->
                        <div>
                            <label for="group_ptan" class="block text-sm font-medium text-gray-700 mb-1">Group
                                PTAN</label>
                            <input type="text" id="group_ptan" name="group_ptan" placeholder="PTAN"
                                class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand focus:border-transparent">
                            <div class="error-message mt-1 text-sm text-red-600 hidden"></div>
                        </div>

                        <!-- Provider SSN -->
                        <div>
                            <label for="provider_ssn" class="block text-sm font-medium text-gray-700 mb-1">
                                Provider SSN <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="provider_ssn" name="provider_ssn" required
                                placeholder="SSN Number"
                                class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand focus:border-transparent">
                            <div class="error-message mt-1 text-sm text-red-600 hidden"></div>
                        </div>

                        <!-- Provider DOB -->
                        <div>
                            <label for="provider_dob" class="block text-sm font-medium text-gray-700 mb-1">
                                Provider DOB <span class="text-red-500">*</span>
                            </label>
                            <input type="date" id="provider_dob" name="provider_dob" required placeholder="DOB"
                                class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand focus:border-transparent">
                            <div class="error-message mt-1 text-sm text-red-600 hidden"></div>
                        </div>

                        <!-- Billing Software -->
                        <div class="md:col-span-2">
                            <label for="billing_software" class="block text-sm font-medium text-gray-700 mb-1">Billing
                                Software In Use</label>
                            <select id="billing_software" name="billing_software"
                                class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand focus:border-transparent">
                                <option value="">Select Software</option>
                                <option value="dentrix">Dentrix</option>
                                <option value="eaglesoft">Eaglesoft</option>
                                <option value="open_dental">Open Dental</option>
                                <option value="softdent">SoftDent</option>
                                <option value="practice_works">Practice Works</option>
                                <option value="dental_ease">Dental Ease</option>
                                <option value="other">Other</option>
                            </select>
                            <div class="error-message mt-1 text-sm text-red-600 hidden"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Step 4: Terms & Agreement -->
            <div id="step-4" class="step-content hidden">
                <div class="text-center mb-8">
                    <h3 class="text-2xl font-semibold text-gray-900 mb-2">Current Plan</h3>
                    <p class="text-gray-600">Add-On Summary</p>
                </div>

                <div class="max-w-4xl mx-auto">
                    <!-- Plan Summary Table -->
                    <div class="bg-white rounded-lg border border-gray-200 overflow-hidden mb-8">
                        <div class="grid grid-cols-2 bg-brand text-white">
                            <div class="px-6 py-4 font-semibold">Plan</div>
                            <div class="px-6 py-4 font-semibold text-right">Price</div>
                        </div>
                        <div class="grid grid-cols-2 border-b border-gray-200">
                            <div class="px-6 py-4">
                                <div class="font-medium text-gray-900" id="selected-plan-display">Large (Monthly)
                                </div>
                                <ul class="mt-2 text-sm text-gray-600 space-y-1">
                                    <li>• Daily Electronic And Paper Claim Submission</li>
                                    <li>• Claim Correction And/Or Re-Submission</li>
                                    <li>• Charge Entry</li>
                                    <li>• Government, Commercial And Private Billing</li>
                                    <li>• Dental Management</li>
                                </ul>
                                <button type="button" class="text-brand text-sm mt-2 hover:underline">View
                                    More...</button>
                            </div>
                            <div class="px-6 py-4 text-right">
                                <div class="text-xl font-bold text-gray-900" id="selected-plan-price">2.99%</div>
                            </div>
                        </div>

                        <!-- Add-ons -->
                        <div id="addon-summary" class="hidden">
                            <!-- Benefit Verification Add-on -->
                            <div class="grid grid-cols-2 border-b border-gray-200 addon-item"
                                data-addon="benefit_verification">
                                <div class="px-6 py-4">
                                    <div class="font-medium text-gray-900">Benefit Verification (Add On)</div>
                                </div>
                                <div class="px-6 py-4 text-right">
                                    <div class="text-lg font-semibold text-gray-900">$7/Month</div>
                                </div>
                            </div>

                            <!-- Pre Authorization Add-on -->
                            <div class="grid grid-cols-2 border-b border-gray-200 addon-item"
                                data-addon="pre_authorization">
                                <div class="px-6 py-4">
                                    <div class="font-medium text-gray-900">Pre Authorization (Add On)</div>
                                </div>
                                <div class="px-6 py-4 text-right">
                                    <div class="text-lg font-semibold text-gray-900">$10/Month</div>
                                </div>
                            </div>

                            <!-- Dental Auditing Add-on -->
                            <div class="grid grid-cols-2 border-b border-gray-200 addon-item"
                                data-addon="dental_auditing">
                                <div class="px-6 py-4">
                                    <div class="font-medium text-gray-900">Dental Auditing (Add On)</div>
                                </div>
                                <div class="px-6 py-4 text-right">
                                    <div class="text-lg font-semibold text-gray-900">$350/Month</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="flex items-start space-x-3 mb-8">
                        <input type="checkbox" id="terms_agreement" name="terms_agreement" required
                            class="mt-1 w-5 h-5 text-brand border-gray-300 rounded focus:ring-brand">
                        <label for="terms_agreement" class="text-sm text-gray-700 cursor-pointer">
                            I Agree To The
                            <a href="#" class="text-brand hover:underline font-medium">Terms & Conditions</a>
                            And Authorize Meds Dental To Begin The Onboarding Process For My Practice.
                        </label>
                    </div>
                    <div class="error-message mt-1 text-sm text-red-600 hidden" id="terms-error"></div>
                </div>
            </div>

            <!-- Step 5: Schedule A Call -->
            <div id="step-5" class="step-content hidden">
                <div class="max-w-6xl mx-auto">
                    <div class="bg-white rounded-lg border border-gray-200 p-8">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- Left Side - Call Info -->
                            <div>
                                <h3 class="text-2xl font-semibold text-gray-900 mb-4">Schedule A Call</h3>
                                <p class="text-gray-600 mb-6">Please select a timing for a 30 minutes call with our CRM
                                    team</p>

                                <!-- Date Selection -->
                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Select Date</label>
                                    <select id="timezone"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand focus:border-transparent mb-4">
                                        <option value="EST">Eastern Standard Time (EST)</option>
                                        <option value="CST">Central Standard Time (CST)</option>
                                        <option value="MST">Mountain Standard Time (MST)</option>
                                        <option value="PST">Pacific Standard Time (PST)</option>
                                    </select>

                                    <!-- Calendar -->
                                    <div class="border border-gray-200 rounded-lg p-4">
                                        <div class="grid grid-cols-7 gap-1 text-center text-sm">
                                            <div class="font-medium text-gray-500 py-2">Mo</div>
                                            <div class="font-medium text-gray-500 py-2">Tu</div>
                                            <div class="font-medium text-gray-500 py-2">We</div>
                                            <div class="font-medium text-gray-500 py-2">Th</div>
                                            <div class="font-medium text-gray-500 py-2">Fr</div>
                                            <div class="font-medium text-gray-500 py-2">Sa</div>
                                            <div class="font-medium text-gray-500 py-2">Su</div>

                                            <!-- Calendar Days -->
                                            <div class="py-2 text-gray-400">1</div>
                                            <div class="py-2 text-gray-400">2</div>
                                            <div class="py-2 text-gray-400">3</div>
                                            <div class="py-2 text-gray-400">4</div>
                                            <div class="py-2 text-gray-400">5</div>
                                            <div class="py-2 text-red-500">6</div>
                                            <div class="py-2 text-red-500">7</div>

                                            <div class="py-2 text-gray-400">8</div>
                                            <div class="py-2 text-gray-400">9</div>
                                            <div class="py-2 text-gray-400">10</div>
                                            <div class="py-2 text-gray-400">11</div>
                                            <div class="py-2 text-gray-400">12</div>
                                            <div class="py-2 text-red-500">13</div>
                                            <div class="py-2 text-red-500">14</div>

                                            <div class="py-2 text-gray-400">15</div>
                                            <div class="py-2 text-gray-400">16</div>
                                            <div class="py-2 text-gray-400">17</div>
                                            <div class="py-2 cursor-pointer hover:bg-brand hover:text-white rounded calendar-day brand-bg text-white"
                                                data-date="2024-06-18">18</div>
                                            <div class="py-2 cursor-pointer hover:bg-brand hover:text-white rounded calendar-day"
                                                data-date="2024-06-19">19</div>
                                            <div class="py-2 text-red-500">20</div>
                                            <div class="py-2 text-red-500">21</div>

                                            <div class="py-2 cursor-pointer hover:bg-brand hover:text-white rounded calendar-day"
                                                data-date="2024-06-22">22</div>
                                            <div class="py-2 cursor-pointer hover:bg-brand hover:text-white rounded calendar-day"
                                                data-date="2024-06-23">23</div>
                                            <div class="py-2 cursor-pointer hover:bg-brand hover:text-white rounded calendar-day"
                                                data-date="2024-06-24">24</div>
                                            <div class="py-2 cursor-pointer hover:bg-brand hover:text-white rounded calendar-day"
                                                data-date="2024-06-25">25</div>
                                            <div class="py-2 cursor-pointer hover:bg-brand hover:text-white rounded calendar-day"
                                                data-date="2024-06-26">26</div>
                                            <div class="py-2 text-red-500">27</div>
                                            <div class="py-2 text-red-500">28</div>

                                            <div class="py-2 cursor-pointer hover:bg-brand hover:text-white rounded calendar-day"
                                                data-date="2024-06-29">29</div>
                                            <div class="py-2 cursor-pointer hover:bg-brand hover:text-white rounded calendar-day"
                                                data-date="2024-06-30">30</div>
                                            <div class="py-2 cursor-pointer hover:bg-brand hover:text-white rounded calendar-day"
                                                data-date="2024-07-01">31</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Side - Time Selection -->
                            <div>
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Select Time</label>
                                    <div class="text-lg font-semibold text-gray-900 mb-4" id="selected-date-display">
                                        Thurs, June 18</div>
                                </div>

                                <!-- Time Slots -->
                                <div class="space-y-2 max-h-96 overflow-y-auto">
                                    <div class="text-sm text-gray-500 mb-2">Morning</div>
                                    <button type="button"
                                        class="w-full text-left px-3 py-2 border border-gray-300 rounded-lg hover:border-brand hover:bg-brand hover:text-white transition-colors time-slot"
                                        data-time="09:45 AM - 10:15 AM">
                                        09:45 AM - 10:15 AM
                                    </button>
                                    <button type="button"
                                        class="w-full text-left px-3 py-2 border border-gray-300 rounded-lg hover:border-brand hover:bg-brand hover:text-white transition-colors time-slot"
                                        data-time="10:30 AM - 11:00 AM">
                                        10:30 AM - 11:00 AM
                                    </button>
                                    <button type="button"
                                        class="w-full text-left px-3 py-2 border border-gray-300 rounded-lg hover:border-brand hover:bg-brand hover:text-white transition-colors time-slot"
                                        data-time="11:15 AM - 11:45 AM">
                                        11:15 AM - 11:45 AM
                                    </button>

                                    <div class="text-sm text-gray-500 mb-2 mt-4">Afternoon</div>
                                    <button type="button"
                                        class="w-full text-left px-3 py-2 border border-brand bg-brand text-white rounded-lg time-slot selected"
                                        data-time="12:00 PM - 12:30 PM">
                                        12:00 PM - 12:30 PM
                                    </button>
                                    <button type="button"
                                        class="w-full text-left px-3 py-2 border border-gray-300 rounded-lg hover:border-brand hover:bg-brand hover:text-white transition-colors time-slot"
                                        data-time="12:45 PM - 01:15 PM">
                                        12:45 PM - 01:15 PM
                                    </button>
                                    <button type="button"
                                        class="w-full text-left px-3 py-2 border border-gray-300 rounded-lg hover:border-brand hover:bg-brand hover:text-white transition-colors time-slot"
                                        data-time="01:30 PM - 02:00 PM">
                                        01:30 PM - 02:00 PM
                                    </button>
                                </div>

                                <!-- Hidden inputs for selected date and time -->
                                <input type="hidden" id="selected_date" name="selected_date" value="2024-06-18">
                                <input type="hidden" id="selected_time" name="selected_time"
                                    value="12:00 PM - 12:30 PM">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Buttons -->
            <div class="flex justify-between pt-6">
                <button type="button" id="prev-btn"
                    class="hidden px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                    Back
                </button>
                <div class="flex-1"></div>
                <button type="button" id="next-btn"
                    class="px-6 py-3 brand-bg text-white rounded-lg hover:opacity-90 transition-colors">
                    Continue
                </button>
                <button type="button" id="schedule-btn"
                    class="hidden px-6 py-3 brand-bg text-white rounded-lg hover:opacity-90 transition-colors">
                    Schedule Call
                </button>
                <button type="submit" id="submit-btn"
                    class="hidden px-6 py-3 brand-bg text-white rounded-lg hover:opacity-90 transition-colors">
                    Complete Registration
                </button>
            </div>

            <!-- Login Link -->
            <div class="text-center pt-4">
                <p class="text-sm text-gray-600">
                    Already have an account?
                    <a href="{{ route('login') }}" class="font-medium hover:underline brand-text">
                        Sign in
                    </a>
                </p>
            </div>
        </form>
    </div>

    <!-- Additional Styling -->
    <style>
        .pricing-card.selected {
            border-color: #2B8B7F !important;
            border-width: 2px !important;
            box-shadow: 0 0 0 3px rgba(43, 139, 127, 0.1);
        }

        .step-content {
            min-height: 400px;
        }

        #step-2,
        #step-3,
        #step-4,
        #step-5 {
            min-height: 600px;
        }

        /* Step indicator enhancements */
        .step-indicator {
            position: relative;
        }

        .step-indicator .w-12 {
            z-index: 10;
            position: relative;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        /* Brand color text utility */
        .text-brand {
            color: #2B8B7F;
        }

        /* Progress line styling */
        #progress-line {
            z-index: 5;
            background: linear-gradient(90deg, #2B8B7F 0%, #2B8B7F 100%);
        }

        /* Mobile step indicator improvements */
        @media (max-width: 768px) {
            .step-indicator {
                padding: 0 1rem;
            }

            .step-indicator .text-sm {
                font-size: 0.75rem;
            }

            .step-indicator .text-xs {
                font-size: 0.625rem;
            }

            .step-indicator .w-12 {
                width: 2.5rem;
                height: 2.5rem;
            }

            .step-indicator .w-6 {
                width: 1.25rem;
                height: 1.25rem;
            }
        }

        @media (max-width: 640px) {
            .step-indicator {
                overflow-x: auto;
                scrollbar-width: none;
                -ms-overflow-style: none;
                padding: 0 0.5rem;
            }

            .step-indicator::-webkit-scrollbar {
                display: none;
            }

            .step-indicator .text-center {
                min-width: 80px;
            }
        }

        .transition-all {
            transition: all 0.3s ease;
        }

        .pricing-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .brand-bg {
            background-color: #2B8B7F !important;
        }

        .brand-text {
            color: #2B8B7F !important;
        }

        .focus\:ring-brand:focus {
            --tw-ring-color: rgba(43, 139, 127, 0.5);
            box-shadow: 0 0 0 3px rgba(43, 139, 127, 0.1);
        }

        /* Layout transition animations */
        .min-h-screen>div {
            transition: all 0.5s ease-in-out;
        }

        .w-full.max-w-md,
        .w-full.max-w-6xl {
            transition: max-width 0.5s ease-in-out;
        }
    </style>

    <!-- Multi-Step Form JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let currentStep = 1;
            const totalSteps = 5;

            // Elements
            const step1Content = document.getElementById('step-1');
            const step2Content = document.getElementById('step-2');
            const step3Content = document.getElementById('step-3');
            const step4Content = document.getElementById('step-4');
            const step5Content = document.getElementById('step-5');

            const stepIndicators = {
                1: document.getElementById('step1-indicator'),
                2: document.getElementById('step2-indicator'),
                3: document.getElementById('step3-indicator'),
                4: document.getElementById('step4-indicator'),
                5: document.getElementById('step5-indicator')
            };

            const stepTitles = {
                1: document.getElementById('step1-title'),
                2: document.getElementById('step2-title'),
                3: document.getElementById('step3-title'),
                4: document.getElementById('step4-title'),
                5: document.getElementById('step5-title')
            };

            const stepSubtitles = {
                1: document.getElementById('step1-subtitle'),
                2: document.getElementById('step2-subtitle'),
                3: document.getElementById('step3-subtitle'),
                4: document.getElementById('step4-subtitle'),
                5: document.getElementById('step5-subtitle')
            };

            const formTitle = document.getElementById('form-title');
            const formSubtitle = document.getElementById('form-subtitle');
            const googleSignupSection = document.getElementById('google-signup-section');
            const nextBtn = document.getElementById('next-btn');
            const prevBtn = document.getElementById('prev-btn');
            const submitBtn = document.getElementById('submit-btn');
            const scheduleBtn = document.getElementById('schedule-btn');
            const monthlyBtn = document.getElementById('monthly-btn');
            const annuallyBtn = document.getElementById('annually-btn');
            const pricingCards = document.querySelectorAll('.pricing-card');

            // Layout control functions
            function setLayoutForStep(step) {
                const leftPanel = document.querySelector('.min-h-screen > div:first-child');
                const rightPanel = document.querySelector('.min-h-screen > div:last-child');
                const formContainer = document.querySelector('.w-full.max-w-md, .w-full.max-w-6xl');

                if (step === 1) {
                    // Step 1: Show split layout
                    setTimeout(() => {
                        leftPanel.classList.remove('hidden');
                        leftPanel.classList.add('lg:flex', 'lg:w-1/2');
                        rightPanel.classList.remove('lg:w-full');
                        rightPanel.classList.add('lg:w-1/2');
                        formContainer.classList.remove('max-w-6xl');
                        formContainer.classList.add('max-w-md');
                    }, 50);
                } else {
                    // Steps 2-5: Hide left panel, expand right panel
                    setTimeout(() => {
                        leftPanel.classList.add('hidden');
                        leftPanel.classList.remove('lg:flex', 'lg:w-1/2');
                        rightPanel.classList.remove('lg:w-1/2');
                        rightPanel.classList.add('lg:w-full');
                        formContainer.classList.remove('max-w-md');
                        formContainer.classList.add('max-w-6xl');
                    }, 50);
                }
            }

            // Step navigation functions
            function showStep(step) {
                // Hide all steps
                step1Content.classList.add('hidden');
                step2Content.classList.add('hidden');
                step3Content.classList.add('hidden');
                step4Content.classList.add('hidden');
                step5Content.classList.add('hidden');

                // Set layout for current step
                setLayoutForStep(step);

                // Update step indicators
                updateStepIndicators(step);

                // Show current step and update content
                if (step === 1) {
                    // Step 1: Show split layout
                    step1Content.classList.remove('hidden');
                    formTitle.textContent = 'Create your account';
                    formSubtitle.textContent =
                        'Welcome to Meds Dental! Your gateway to streamlined dental career growth begins here';
                    googleSignupSection.classList.remove('hidden');

                    // Update buttons
                    prevBtn.classList.add('hidden');
                    nextBtn.classList.remove('hidden');
                    nextBtn.textContent = 'Continue';
                    submitBtn.classList.add('hidden');
                    scheduleBtn.classList.add('hidden');
                } else if (step === 2) {
                    step2Content.classList.remove('hidden');
                    formTitle.textContent = 'Choose Your Plan';
                    formSubtitle.textContent =
                        'Your package is included with 10 benefit verifications & 5 Pre Authorization';
                    googleSignupSection.classList.add('hidden');

                    // Update buttons
                    prevBtn.classList.remove('hidden');
                    nextBtn.classList.remove('hidden');
                    nextBtn.textContent = 'Continue';
                    submitBtn.classList.add('hidden');
                    scheduleBtn.classList.add('hidden');

                } else if (step === 3) {
                    step3Content.classList.remove('hidden');
                    formTitle.textContent = 'Practice Information';
                    formSubtitle.textContent = "Let's get your Practice Information";
                    googleSignupSection.classList.add('hidden');

                    // Update buttons
                    prevBtn.classList.remove('hidden');
                    nextBtn.classList.remove('hidden');
                    nextBtn.textContent = 'Continue';
                    submitBtn.classList.add('hidden');
                    scheduleBtn.classList.add('hidden');

                } else if (step === 4) {
                    step4Content.classList.remove('hidden');
                    formTitle.textContent = 'Terms & Agreement';
                    formSubtitle.textContent = 'Accept terms & conditions';
                    googleSignupSection.classList.add('hidden');

                    // Update plan summary
                    updatePlanSummary();

                    // Update buttons
                    prevBtn.classList.remove('hidden');
                    nextBtn.classList.remove('hidden');
                    nextBtn.textContent = 'Continue';
                    submitBtn.classList.add('hidden');
                    scheduleBtn.classList.add('hidden');

                } else if (step === 5) {
                    step5Content.classList.remove('hidden');
                    formTitle.textContent = 'Schedule A Call';
                    formSubtitle.textContent = "Let's schedule a call";
                    googleSignupSection.classList.add('hidden');

                    // Update buttons
                    prevBtn.classList.remove('hidden');
                    nextBtn.classList.add('hidden');
                    submitBtn.classList.remove('hidden');
                    scheduleBtn.classList.add('hidden');
                }

                currentStep = step;
            }

            // Update step indicators
            function updateStepIndicators(currentStep) {
                // Update progress line
                const progressLine = document.getElementById('progress-line');
                const progressPercentage = ((currentStep - 1) / (totalSteps - 1)) * 100;
                progressLine.style.width = `${progressPercentage}%`;

                // Update step subtitles
                updateStepSubtitles(currentStep);

                for (let i = 1; i <= totalSteps; i++) {
                    const indicator = stepIndicators[i];
                    const title = document.getElementById(`step${i}-title`);
                    const subtitle = document.getElementById(`step${i}-subtitle`);

                    if (i < currentStep) {
                        // Completed step - brand background with white checkmark
                        indicator.classList.remove('border-gray-300', 'bg-gray-100', 'text-gray-400', 'bg-white',
                            'text-brand', 'border-white');
                        indicator.classList.add('border-brand', 'bg-brand', 'text-white');
                        title.classList.remove('text-gray-500', 'text-green-600');
                        title.classList.add('step-title');
                        subtitle.classList.remove('text-gray-400');
                        subtitle.classList.add('text-gray-500');

                        // Show checkmark for completed steps
                        indicator.innerHTML = `
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        `;
                    } else if (i === currentStep) {
                        // Current step - green background with white border and white icon
                        indicator.classList.remove('border-gray-300', 'bg-gray-100', 'text-gray-400', 'bg-brand',
                            'text-brand', 'bg-white');
                        indicator.classList.add('border-white', 'text-white', 'step-active');
                        title.classList.remove('text-gray-500');
                        //title.classList.add('text-green-600');
                        subtitle.classList.remove('text-gray-400');
                        subtitle.classList.add('text-gray-500');

                        // Show appropriate icon for current step
                        let iconSvg = '';
                        switch (i) {
                            case 1:
                                iconSvg = `
                                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                `;
                                break;
                            case 2:
                                iconSvg = `
                                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                                    </svg>
                                `;
                                break;
                            case 3:
                                iconSvg = `
                                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-6a1 1 0 00-1-1H9a1 1 0 00-1 1v6a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clip-rule="evenodd"></path>
                                    </svg>
                                `;
                                break;
                            case 4:
                                iconSvg = `
                                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clip-rule="evenodd"></path>
                                    </svg>
                                `;
                                break;
                            case 5:
                                iconSvg = `
                                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                    </svg>
                                `;
                                break;
                        }
                        indicator.innerHTML = iconSvg;
                    } else {
                        // Future step - gray background with gray border and gray icon
                        indicator.classList.remove('border-brand', 'bg-brand', 'text-white', 'bg-white',
                            'text-brand', 'border-white', 'bg-green-500', 'step-active');
                        indicator.classList.add('border-gray-300', 'bg-gray-100', 'text-gray-400');
                        title.classList.remove('text-green-600');
                        title.classList.add('text-gray-500');
                        subtitle.classList.remove('text-gray-500');
                        subtitle.classList.add('text-gray-400');

                        // Show appropriate icon for future steps
                        let iconSvg = '';
                        switch (i) {
                            case 2:
                                iconSvg = `
                                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"></path>
                                    </svg>
                                `;
                                break;
                            case 3:
                                iconSvg = `
                                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-6a1 1 0 00-1-1H9a1 1 0 00-1 1v6a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clip-rule="evenodd"></path>
                                    </svg>
                                `;
                                break;
                            case 4:
                                iconSvg = `
                                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clip-rule="evenodd"></path>
                                    </svg>
                                `;
                                break;
                            case 5:
                                iconSvg = `
                                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                    </svg>
                                `;
                                break;
                        }
                        indicator.innerHTML = iconSvg;
                    }
                }
            }

            // Update step subtitles dynamically
            function updateStepSubtitles(currentStep) {
                const subtitles = {
                    1: currentStep > 1 ? 'Completed' : 'Create your account',
                    2: currentStep > 2 ? 'Completed' : 'Let\'s finalize your plan',
                    3: currentStep > 3 ? 'Completed' : 'Let\'s get your Practice Information',
                    4: currentStep > 4 ? 'Completed' : 'Accept terms & conditions',
                    5: 'Let\'s schedule a call'
                };

                for (let i = 1; i <= totalSteps; i++) {
                    const subtitle = document.getElementById(`step${i}-subtitle`);
                    if (subtitle) {
                        subtitle.textContent = subtitles[i];
                    }
                }
            }

            // Update plan summary for step 4
            function updatePlanSummary() {
                const selectedPlan = document.getElementById('selected_plan').value;
                const billingCycle = document.getElementById('billing_cycle').value;
                const selectedPlanDisplay = document.getElementById('selected-plan-display');
                const selectedPlanPrice = document.getElementById('selected-plan-price');
                const addonSummary = document.getElementById('addon-summary');

                // Update plan display
                if (selectedPlan && selectedPlanDisplay) {
                    const planName = selectedPlan.charAt(0).toUpperCase() + selectedPlan.slice(1);
                    const cycle = billingCycle === 'monthly' ? 'Monthly' : 'Annually';
                    selectedPlanDisplay.textContent = `${planName} (${cycle})`;

                    // Update price based on plan and cycle
                    let price = '';
                    if (selectedPlan === 'small') {
                        price = billingCycle === 'monthly' ? '$1,250' : '$12,500';
                    } else if (selectedPlan === 'medium') {
                        price = billingCycle === 'monthly' ? '4.99%' : '4.49%';
                    } else if (selectedPlan === 'large') {
                        price = billingCycle === 'monthly' ? '3.49%' : '3.19%';
                    }
                    selectedPlanPrice.textContent = price;
                }

                // Show/hide add-ons
                const selectedServices = document.querySelectorAll('input[name="additional_services[]"]:checked');
                if (selectedServices.length > 0) {
                    addonSummary.classList.remove('hidden');
                    // Hide all addon items first
                    document.querySelectorAll('.addon-item').forEach(item => item.classList.add('hidden'));

                    // Show selected addons
                    selectedServices.forEach(service => {
                        const addonItem = document.querySelector(
                            `.addon-item[data-addon="${service.value}"]`);
                        if (addonItem) {
                            addonItem.classList.remove('hidden');
                        }
                    });
                } else {
                    addonSummary.classList.add('hidden');
                }
            }

            // Validation functions
            function validateStep1() {
                const requiredFields = ['name', 'email', 'password', 'password_confirmation'];
                let isValid = true;

                // Clear previous errors
                document.querySelectorAll('#step-1 .error-message').forEach(el => {
                    el.classList.add('hidden');
                    el.textContent = '';
                });

                // Reset field styles
                document.querySelectorAll('#step-1 input').forEach(input => {
                    input.classList.remove('border-red-500');
                });

                requiredFields.forEach(fieldName => {
                    const field = document.getElementById(fieldName);
                    const errorDiv = field.parentNode.querySelector('.error-message');

                    if (!field.value.trim()) {
                        isValid = false;
                        field.classList.add('border-red-500');
                        errorDiv.textContent = `${fieldName.replace('_', ' ')} is required`;
                        errorDiv.classList.remove('hidden');
                    }
                });

                // Email validation
                const email = document.getElementById('email');
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (email.value && !emailRegex.test(email.value)) {
                    isValid = false;
                    email.classList.add('border-red-500');
                    const errorDiv = email.parentNode.querySelector('.error-message');
                    errorDiv.textContent = 'Please enter a valid email address';
                    errorDiv.classList.remove('hidden');
                }

                // Password confirmation
                const password = document.getElementById('password');
                const passwordConfirmation = document.getElementById('password_confirmation');
                if (password.value && passwordConfirmation.value && password.value !== passwordConfirmation.value) {
                    isValid = false;
                    passwordConfirmation.classList.add('border-red-500');
                    const errorDiv = passwordConfirmation.parentNode.querySelector('.error-message');
                    errorDiv.textContent = 'Passwords do not match';
                    errorDiv.classList.remove('hidden');
                }

                return isValid;
            }

            function validateStep2() {
                let isValid = true;

                // Clear previous errors
                document.querySelectorAll('#step-2 .error-message').forEach(el => {
                    el.classList.add('hidden');
                    el.textContent = '';
                });

                // Practice type validation
                //const practiceType = document.getElementById('practice_type');
                //if (!practiceType.value) {
                //    isValid = false;
                //    practiceType.classList.add('border-red-500');
                //    const errorDiv = practiceType.parentNode.querySelector('.error-message');
                //    errorDiv.textContent = 'Please select a practice type';
                //   errorDiv.classList.remove('hidden');
                //} else {
                //    practiceType.classList.remove('border-red-500');
                //}

                // Plan selection validation
                const selectedPlan = document.getElementById('selected_plan');
                if (!selectedPlan.value) {
                    isValid = false;
                    // Show error message for plan selection
                    alert('Please select a pricing plan');
                }

                return isValid;
            }

            function validateStep3() {
                const requiredFields = ['practice_name', 'practice_address', 'tax_id', 'individual_npi',
                    'individual_ptan', 'provider_ssn', 'provider_dob'
                ];
                let isValid = true;

                // Clear previous errors
                document.querySelectorAll('#step-3 .error-message').forEach(el => {
                    el.classList.add('hidden');
                    el.textContent = '';
                });

                // Reset field styles
                document.querySelectorAll('#step-3 input, #step-3 select').forEach(input => {
                    input.classList.remove('border-red-500');
                });

                requiredFields.forEach(fieldName => {
                    const field = document.getElementById(fieldName);
                    const errorDiv = field.parentNode.querySelector('.error-message');

                    if (!field.value.trim()) {
                        isValid = false;
                        field.classList.add('border-red-500');
                        errorDiv.textContent = `${fieldName.replace('_', ' ')} is required`;
                        errorDiv.classList.remove('hidden');
                    }
                });

                return isValid;
            }

            function validateStep4() {
                let isValid = true;
                const termsCheckbox = document.getElementById('terms_agreement');
                const termsError = document.getElementById('terms-error');

                // Clear previous errors
                termsError.classList.add('hidden');
                termsError.textContent = '';

                if (!termsCheckbox.checked) {
                    isValid = false;
                    termsError.textContent = 'You must agree to the terms and conditions';
                    termsError.classList.remove('hidden');
                }

                return isValid;
            }

            function validateStep5() {
                // Step 5 validation (optional - could validate date/time selection)
                return true;
            }

            // Event listeners
            nextBtn.addEventListener('click', function(e) {
                e.preventDefault();

                if (currentStep === 1 && validateStep1()) {
                    // Store step 1 data in sessionStorage
                    const step1Data = {
                        name: document.getElementById('name').value,
                        last_name: document.getElementById('last_name').value,
                        email: document.getElementById('email').value,
                        phone: document.getElementById('phone').value,
                        password: document.getElementById('password').value,
                        password_confirmation: document.getElementById('password_confirmation').value
                    };
                    sessionStorage.setItem('registrationStep1', JSON.stringify(step1Data));
                    showStep(2);
                } else if (currentStep === 2 && validateStep2()) {
                    showStep(3);
                } else if (currentStep === 3 && validateStep3()) {
                    showStep(4);
                } else if (currentStep === 4 && validateStep4()) {
                    showStep(5);
                }
            });

            prevBtn.addEventListener('click', function(e) {
                e.preventDefault();
                if (currentStep > 1) {
                    showStep(currentStep - 1);
                }
            });

            // Billing cycle toggle
            monthlyBtn.addEventListener('click', function() {
                monthlyBtn.classList.add('brand-bg', 'text-white');
                monthlyBtn.classList.remove('text-gray-600');
                annuallyBtn.classList.remove('brand-bg', 'text-white');
                annuallyBtn.classList.add('text-gray-600');

                document.getElementById('billing_cycle').value = 'monthly';

                // Show monthly prices
                document.querySelectorAll('.monthly-price').forEach(el => el.classList.remove('hidden'));
                document.querySelectorAll('.monthly-desc').forEach(el => el.classList.remove('hidden'));
                document.querySelectorAll('.annually-price').forEach(el => el.classList.add('hidden'));
                document.querySelectorAll('.annually-desc').forEach(el => el.classList.add('hidden'));
            });

            annuallyBtn.addEventListener('click', function() {
                annuallyBtn.classList.add('brand-bg', 'text-white');
                annuallyBtn.classList.remove('text-gray-600');
                monthlyBtn.classList.remove('brand-bg', 'text-white');
                monthlyBtn.classList.add('text-gray-600');

                document.getElementById('billing_cycle').value = 'annually';

                // Show annual prices
                document.querySelectorAll('.annually-price').forEach(el => el.classList.remove('hidden'));
                document.querySelectorAll('.annually-desc').forEach(el => el.classList.remove('hidden'));
                document.querySelectorAll('.monthly-price').forEach(el => el.classList.add('hidden'));
                document.querySelectorAll('.monthly-desc').forEach(el => el.classList.add('hidden'));
            });

            // Pricing card selection
            pricingCards.forEach(card => {
                card.addEventListener('click', function() {
                    // Remove selection from all cards
                    pricingCards.forEach(c => {
                        c.classList.remove('border-brand', 'border-2');
                        c.classList.add('border-gray-200');
                    });

                    // Add selection to clicked card
                    this.classList.remove('border-gray-200');
                    this.classList.add('border-brand', 'border-2');

                    // Update hidden input
                    document.getElementById('selected_plan').value = this.dataset.plan;
                });
            });

            // Calendar and time selection for Step 5
            document.querySelectorAll('.calendar-day').forEach(day => {
                day.addEventListener('click', function() {
                    // Remove selection from all days
                    document.querySelectorAll('.calendar-day').forEach(d => {
                        d.classList.remove('brand-bg', 'text-white');
                    });

                    // Add selection to clicked day
                    this.classList.add('brand-bg', 'text-white');

                    // Update selected date
                    document.getElementById('selected_date').value = this.dataset.date;

                    // Update display
                    const selectedDateDisplay = document.getElementById('selected-date-display');
                    const date = new Date(this.dataset.date);
                    const options = {
                        weekday: 'short',
                        month: 'long',
                        day: 'numeric'
                    };
                    selectedDateDisplay.textContent = date.toLocaleDateString('en-US', options);
                });
            });

            document.querySelectorAll('.time-slot').forEach(slot => {
                slot.addEventListener('click', function() {
                    // Remove selection from all time slots
                    document.querySelectorAll('.time-slot').forEach(s => {
                        s.classList.remove('border-brand', 'bg-brand', 'text-white',
                            'selected');
                        s.classList.add('border-gray-300');
                    });

                    // Add selection to clicked slot
                    this.classList.remove('border-gray-300');
                    this.classList.add('border-brand', 'bg-brand', 'text-white', 'selected');

                    // Update selected time
                    document.getElementById('selected_time').value = this.dataset.time;
                });
            });

            // Form submission
            document.getElementById('registration-form').addEventListener('submit', function(e) {
                if (currentStep < 5) {
                    e.preventDefault();
                    return;
                }

                // Validate current step before submission
                let isValid = true;
                switch (currentStep) {
                    case 1:
                        isValid = validateStep1();
                        break;
                    case 2:
                        isValid = validateStep2();
                        break;
                    case 3:
                        isValid = validateStep3();
                        break;
                    case 4:
                        isValid = validateStep4();
                        break;
                    case 5:
                        isValid = validateStep5();
                        break;
                }

                if (!isValid) {
                    e.preventDefault();
                }
            });

            // Initialize
            showStep(1);

            // Restore step 1 data if available
            const savedStep1Data = sessionStorage.getItem('registrationStep1');
            if (savedStep1Data) {
                const data = JSON.parse(savedStep1Data);
                Object.keys(data).forEach(key => {
                    const field = document.getElementById(key);
                    if (field) {
                        field.value = data[key];
                    }
                });
            }
        });
    </script>
</x-guest-layout>

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('last_name')->nullable()->after('name');
            $table->string('phone')->nullable()->after('email');
            $table->string('practice_type')->nullable()->after('phone');
            $table->string('selected_plan')->nullable()->after('practice_type');
            $table->string('billing_cycle')->nullable()->after('selected_plan');
            $table->json('additional_services')->nullable()->after('billing_cycle');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'last_name',
                'phone',
                'practice_type',
                'selected_plan',
                'billing_cycle',
                'additional_services'
            ]);
        });
    }
};

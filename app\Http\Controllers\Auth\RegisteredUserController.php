<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Illuminate\View\View;

class RegisteredUserController extends Controller
{
    /**
     * Display the registration view.
     */
    public function create(): View
    {
        return view('auth.register');
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'last_name' => ['nullable', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
            'phone' => ['nullable', 'string', 'max:20'],
            'practice_type' => ['required', 'string', 'max:255'],
            'selected_plan' => ['required', 'string', 'in:small,medium,large'],
            'billing_cycle' => ['required', 'string', 'in:monthly,annually'],
            'additional_services' => ['nullable', 'array'],
            'additional_services.*' => ['string', 'in:benefit_verification,pre_authorization,dental_auditing'],
            'practice_name' => ['required', 'string', 'max:255'],
            'practice_address' => ['required', 'string', 'max:500'],
            'tax_id' => ['required', 'string', 'max:50'],
            'major_services' => ['nullable', 'string', 'max:255'],
            'num_providers' => ['nullable', 'integer', 'min:1'],
            'individual_npi' => ['required', 'string', 'max:50'],
            'individual_ptan' => ['required', 'string', 'max:50'],
            'group_npi' => ['nullable', 'string', 'max:50'],
            'group_ptan' => ['nullable', 'string', 'max:50'],
            'provider_ssn' => ['required', 'string', 'max:50'],
            'provider_dob' => ['required', 'date'],
            'billing_software' => ['nullable', 'string', 'max:255'],
            'terms_agreement' => ['required', 'accepted'],
            'selected_date' => ['nullable', 'date'],
            'selected_time' => ['nullable', 'string', 'max:50'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        $user = User::create([
            'name' => $request->name,
            'last_name' => $request->last_name,
            'email' => $request->email,
            'phone' => $request->phone,
            'practice_type' => $request->practice_type,
            'selected_plan' => $request->selected_plan,
            'billing_cycle' => $request->billing_cycle,
            'additional_services' => $request->additional_services ? json_encode($request->additional_services) : null,
            'practice_name' => $request->practice_name,
            'practice_address' => $request->practice_address,
            'tax_id' => $request->tax_id,
            'major_services' => $request->major_services,
            'num_providers' => $request->num_providers,
            'individual_npi' => $request->individual_npi,
            'individual_ptan' => $request->individual_ptan,
            'group_npi' => $request->group_npi,
            'group_ptan' => $request->group_ptan,
            'provider_ssn' => $request->provider_ssn,
            'provider_dob' => $request->provider_dob,
            'billing_software' => $request->billing_software,
            'selected_date' => $request->selected_date,
            'selected_time' => $request->selected_time,
            'password' => Hash::make($request->password),
        ]);

        event(new Registered($user));

        Auth::login($user);

        return redirect(route('dashboard', absolute: false));
    }
}

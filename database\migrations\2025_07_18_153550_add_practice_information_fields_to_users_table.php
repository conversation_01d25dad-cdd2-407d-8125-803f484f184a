<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Practice Information fields
            $table->string('practice_name')->nullable()->after('additional_services');
            $table->text('practice_address')->nullable()->after('practice_name');
            $table->string('tax_id')->nullable()->after('practice_address');
            $table->string('major_services')->nullable()->after('tax_id');
            $table->integer('num_providers')->nullable()->after('major_services');
            $table->string('individual_npi')->nullable()->after('num_providers');
            $table->string('individual_ptan')->nullable()->after('individual_npi');
            $table->string('group_npi')->nullable()->after('individual_ptan');
            $table->string('group_ptan')->nullable()->after('group_npi');
            $table->string('provider_ssn')->nullable()->after('group_ptan');
            $table->date('provider_dob')->nullable()->after('provider_ssn');
            $table->string('billing_software')->nullable()->after('provider_dob');

            // Scheduling fields
            $table->date('selected_date')->nullable()->after('billing_software');
            $table->string('selected_time')->nullable()->after('selected_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'practice_name',
                'practice_address',
                'tax_id',
                'major_services',
                'num_providers',
                'individual_npi',
                'individual_ptan',
                'group_npi',
                'group_ptan',
                'provider_ssn',
                'provider_dob',
                'billing_software',
                'selected_date',
                'selected_time'
            ]);
        });
    }
};

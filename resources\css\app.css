@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

    body {
        font-family: 'Poppins', ui-sans-serif, system-ui, sans-serif;
    }
}

:root {
    --color-brand-primary: #2B8B7F;
    --color-text-primary: #283875;
    --color-text-secondary: #424242B2;
}

/* Custom utility classes for brand colors */
.bg-brand-primary {
    background-color: var(--color-brand-primary);
}

.text-brand-primary {
    color: var(--color-brand-primary);
}

.text-primary {
    color: var(--color-text-primary);
}

.step-title {
    color: var(--color-text-primary);
    font-size: 14px;
    font-weight: 800;
}

.step-subtitle {
    color: var(--color-text-secondary);
    font-size: 14px;
    font-weight: 300;
}

.border-brand {
    border-color: var(--color-brand-primary);
}

.hover\:bg-brand-primary:hover {
    background-color: var(--color-brand-primary);
}
.step-active {
    border-color: #195851 !important;
    background-color: #2B8B7F !important;
    color: #fff !important;
}
.step-active svg {
    fill: #fff !important;
}
/* Poppins font weight utilities */
.font-light {
    font-weight: 300;
}

.font-normal {
    font-weight: 400;
}

.font-medium {
    font-weight: 500;
}

.font-semibold {
    font-weight: 600;
}

.font-bold {
    font-weight: 700;
}

/* Other CSS Styling */
.welcome-shadow {
    box-shadow: 0px 2px 5px 0px #DEDEDE1A;
    border: 1px solid #EDEDED;
    border-radius: 12px;
    background-color: #FFFFFF;
}

.dashboard-widget-blue {
    background: linear-gradient(252.35deg, #D3F2FF 5.07%, #43C2F6 183.29%);
    border-radius: 10px;
}

.dashboard-widget-orange {
    background: linear-gradient(254.59deg, #FFDDAD 0.79%, #DA993F 144.11%);
    border-radius: 10px;
}

.dashboard-widget-purple {
    background: linear-gradient(256.71deg, #E1D8FF 0.68%, #967DFD 155.31%);
    border-radius: 10px;
}

.dashboard-widget-margenta {
    background: linear-gradient(255.32deg, #C6DBFF 1.79%, #3B7DFF 204.88%);
    border-radius: 10px;
}

.dashboard-widget-pink {
    background: linear-gradient(256.71deg, #FFA8C1 0.68%, #F35D88 155.31%);
    border-radius: 10px;
}

/* Sidebar specific styles */
.sidebar-bg {
    background-color: #2B8B7F;
}

/* Dashboard specific styles */
.dashboard-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Chart styles */
.chart-bar {
    transition: all 0.3s ease;
    min-height: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-bar:hover {
    opacity: 0.8;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Claims Status Chart specific styles */
.claims-chart-container {
    position: relative;
}

.claims-chart-container::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background-color: #e5e7eb;
}

/* Tooltip styles for chart bars */
.chart-bar[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 10;
    margin-bottom: 4px;
}

/* Custom scrollbar for sidebar */
.sidebar-scroll::-webkit-scrollbar {
    width: 4px;
}

.sidebar-scroll::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar-scroll::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

.sidebar-scroll::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

@layer components {
    /* Authentication Form Styles */
    .auth-input {
        @apply w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent placeholder-gray-400 transition-colors;
    }

    .auth-input:focus {
        border-color: #2B8B7F;
        box-shadow: 0 0 0 3px rgba(43, 139, 127, 0.1);
    }

    .auth-button {
        background-color: #2B8B7F;
        @apply w-full py-3 px-4 text-white font-medium rounded-lg transition-colors;
    }

    .auth-button:hover {
        background-color: #236B61;
    }

    .auth-button:focus {
        background-color: #236B61;
        box-shadow: 0 0 0 3px rgba(43, 139, 127, 0.2);
    }

    .auth-link {
        color: #2B8B7F;
        @apply font-medium hover:underline transition-colors;
    }

    .auth-link:hover {
        color: #236B61;
    }

    /* Google Button Styles */
    .google-button {
        @apply w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors;
    }

    .google-button:hover {
        background-color: #f9fafb;
        border-color: #d1d5db;
    }
}
